<svg width="1800" height="1400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4A90E2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#357ABD;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="grad2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#50C878;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#228B22;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="grad3" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF6B6B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E74C3C;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="grad4" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD93D;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F39C12;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="grad5" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#9B59B6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8E44AD;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="3" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e"/>
    </marker>
  </defs>
  
  <!-- 背景 -->
  <rect width="1800" height="1400" fill="#f8f9fa"/>
  
  <!-- 标题 -->
  <text x="900" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="28" font-weight="bold" fill="#2c3e50">
    JoyAgent-JDGenie 智能体工作流程详解
  </text>
  
  <!-- Agent类型选择层 -->
  <rect x="50" y="80" width="1700" height="100" fill="url(#grad1)" rx="10" filter="url(#shadow)"/>
  <text x="900" y="110" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="white">
    智能体类型选择 (AgentHandlerFactory)
  </text>
  <text x="900" y="135" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="white">
    根据AgentType枚举自动选择: COMPREHENSIVE(1) | WORKFLOW(2) | PLAN_SOLVE(3) | ROUTER(4) | REACT(5)
  </text>
  <text x="900" y="155" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">
    AgentHandlerFactory.getHandler() → 通过support()方法匹配对应的Handler实现
  </text>
  
  <!-- 五种Agent类型 -->
  <rect x="80" y="200" width="280" height="120" fill="#e8f4fd" stroke="#3498db" stroke-width="2" rx="8"/>
  <text x="220" y="225" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2980b9">
    ReAct Agent
  </text>
  <text x="90" y="245" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• ReactImplAgent</text>
  <text x="90" y="265" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• Think → Act 循环</text>
  <text x="90" y="285" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• 推理-行动模式</text>
  <text x="90" y="305" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• 适合复杂推理任务</text>
  
  <rect x="380" y="200" width="280" height="120" fill="#e8f5e8" stroke="#27ae60" stroke-width="2" rx="8"/>
  <text x="520" y="225" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#27ae60">
    Plan&Execute Agent
  </text>
  <text x="390" y="245" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• PlanningAgent + ExecutorAgent</text>
  <text x="390" y="265" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• 计划-执行模式</text>
  <text x="390" y="285" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• 多层级规划</text>
  <text x="390" y="305" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• 适合结构化任务</text>
  
  <rect x="680" y="200" width="280" height="120" fill="#ffeaea" stroke="#e74c3c" stroke-width="2" rx="8"/>
  <text x="820" y="225" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#c0392b">
    Comprehensive Agent
  </text>
  <text x="690" y="245" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• 综合智能体</text>
  <text x="690" y="265" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• 多模式结合</text>
  <text x="690" y="285" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• 自适应选择</text>
  <text x="690" y="305" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• 通用场景</text>
  
  <rect x="980" y="200" width="280" height="120" fill="#fff3cd" stroke="#f39c12" stroke-width="2" rx="8"/>
  <text x="1120" y="225" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#d68910">
    Workflow Agent
  </text>
  <text x="990" y="245" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• 工作流智能体</text>
  <text x="990" y="265" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• DAG执行引擎</text>
  <text x="990" y="285" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• 并发处理</text>
  <text x="990" y="305" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• 复杂流程</text>
  
  <rect x="1280" y="200" width="280" height="120" fill="#f3e5f5" stroke="#9b59b6" stroke-width="2" rx="8"/>
  <text x="1420" y="225" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#8e44ad">
    Router Agent
  </text>
  <text x="1290" y="245" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• 路由智能体</text>
  <text x="1290" y="265" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• 任务分发</text>
  <text x="1290" y="285" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• 智能路由</text>
  <text x="1290" y="305" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• 多Agent协调</text>
  
  <!-- Function Call机制 -->
  <rect x="50" y="350" width="1700" height="80" fill="url(#grad2)" rx="10" filter="url(#shadow)"/>
  <text x="900" y="380" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="white">
    Function Call 机制 (LLM.askTool)
  </text>
  <text x="900" y="405" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="white">
    LLM → ToolCallResponse → BaseAgent.executeTool() → ToolCollection.execute() → 具体工具实现
  </text>
  
  <!-- 工具执行流程 -->
  <rect x="80" y="450" width="320" height="180" fill="#e8f4fd" stroke="#3498db" stroke-width="2" rx="8"/>
  <text x="240" y="475" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2980b9">
    工具调用流程
  </text>
  <text x="90" y="500" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">1. LLM.askTool()发起调用</text>
  <text x="90" y="520" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">2. 解析ToolCall参数</text>
  <text x="90" y="540" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">3. ToolCollection路由</text>
  <text x="90" y="560" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">4. BaseTool.execute()执行</text>
  <text x="90" y="580" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">5. 结果返回Memory</text>
  <text x="90" y="600" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">6. SSE流式输出</text>
  
  <!-- 代码执行机制 -->
  <rect x="420" y="450" width="320" height="180" fill="#e8f5e8" stroke="#27ae60" stroke-width="2" rx="8"/>
  <text x="580" y="475" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#27ae60">
    代码执行机制
  </text>
  <text x="430" y="500" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• CodeInterpreterTool</text>
  <text x="430" y="520" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• Python沙箱环境</text>
  <text x="430" y="540" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• CIAgent + PythonInterpreterTool</text>
  <text x="430" y="560" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• 支持pandas/numpy/matplotlib</text>
  <text x="430" y="580" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• 文件处理和图表生成</text>
  <text x="430" y="600" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• 流式结果输出</text>
  
  <!-- 网络搜索机制 -->
  <rect x="760" y="450" width="320" height="180" fill="#ffeaea" stroke="#e74c3c" stroke-width="2" rx="8"/>
  <text x="920" y="475" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#c0392b">
    网络搜索机制
  </text>
  <text x="770" y="500" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• DeepSearchTool</text>
  <text x="770" y="520" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• 查询分解(query_decompose)</text>
  <text x="770" y="540" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• 多引擎搜索(MixSearch)</text>
  <text x="770" y="560" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• Bing/Jina/Sogou/Serper</text>
  <text x="770" y="580" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• 内容抓取和去重</text>
  <text x="770" y="600" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• 推理验证(search_reasoning)</text>
  
  <!-- 上下文管理机制 -->
  <rect x="1100" y="450" width="320" height="180" fill="#fff3cd" stroke="#f39c12" stroke-width="2" rx="8"/>
  <text x="1260" y="475" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#d68910">
    上下文管理机制
  </text>
  <text x="1110" y="500" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• Memory类管理消息历史</text>
  <text x="1110" y="520" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• 支持USER/ASSISTANT/TOOL角色</text>
  <text x="1110" y="540" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• clearToolContext()清理工具历史</text>
  <text x="1110" y="560" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• 长上下文截断(truncate_files)</text>
  <text x="1110" y="580" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• 跨任务记忆管理</text>
  <text x="1110" y="600" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• 上下文长度自适应</text>
  
  <!-- MCP工具集成 -->
  <rect x="1440" y="450" width="280" height="180" fill="#f3e5f5" stroke="#9b59b6" stroke-width="2" rx="8"/>
  <text x="1580" y="475" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#8e44ad">
    MCP工具集成
  </text>
  <text x="1450" y="500" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• McpTool外部工具调用</text>
  <text x="1450" y="520" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• MCP协议适配</text>
  <text x="1450" y="540" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• 工具发现和注册</text>
  <text x="1450" y="560" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• 参数序列化传递</text>
  <text x="1450" y="580" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• 结果标准化返回</text>
  <text x="1450" y="600" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">• 端口8188服务</text>
</svg>
